run:
  timeout: 10m
  issues-exit-code: 1
  tests: true
  build-tags:
    - integration
  skip-dirs:
    - vendor
    - node_modules
    - .git
    - bin
    - dist
    - scripts
  skip-files:
    - ".*\\.pb\\.go$"
    - ".*_generated\\.go$"
    - ".*\\.gen\\.go$"
    - "mock_.*\\.go$"
  modules-download-mode: readonly
  allow-parallel-runners: true

output:
  format: colored-line-number
  print-issued-lines: true
  print-linter-name: true
  uniq-by-line: true
  path-prefix: ""
  sort-results: true

linters-settings:
  # Go vet settings
  govet:
    check-shadowing: true
    enable-all: true
    disable:
      - fieldalignment # Too noisy for most projects

  # Cyclomatic complexity
  gocyclo:
    min-complexity: 12

  # Duplicate code detection
  dupl:
    threshold: 150

  # Constants detection
  goconst:
    min-len: 3
    min-occurrences: 3
    ignore-tests: true

  # Spelling
  misspell:
    locale: US
    ignore-words:
      - tailscale
      - mcp

  # Line length
  lll:
    line-length: 120
    tab-width: 4

  # Import formatting
  goimports:
    local-prefixes: github.com/hexsleeves/tailscale-mcp-server

  # Go critic settings
  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
    disabled-checks:
      - dupImport
      - ifElseChain
      - octalLiteral
      - whyNoLint
      - wrapperFunc
      - importShadow
      - unnamedResult
    settings:
      captLocal:
        paramsOnly: true
      elseif:
        skipBalanced: true
      hugeParam:
        sizeThreshold: 80
      rangeExprCopy:
        sizeThreshold: 512
      rangeValCopy:
        sizeThreshold: 128
      tooManyResultsChecker:
        maxResults: 5
      truncateCmp:
        skipArchDependent: true
      underef:
        skipRecvDeref: true
      unnamedResult:
        checkExported: true

  # Function length
  funlen:
    lines: 80
    statements: 40
    ignore-comments: true

  # Cognitive complexity
  gocognit:
    min-complexity: 20

  # Magic numbers
  gomnd:
    checks:
      - argument
      - case
      - condition
      - operation
      - return
      - assign
    ignored-numbers:
      - "0"
      - "1"
      - "2"
      - "3"
      - "8"
      - "10"
      - "16"
      - "32"
      - "64"
      - "100"
      - "1000"
      - "1024"
    ignored-functions:
      - "make"
      - "strconv.*"

  # Security
  gosec:
    severity: medium
    confidence: medium
    excludes:
      - G101 # Potential hardcoded credentials
      - G104 # Errors unhandled
      - G204 # Subprocess launched with variable
      - G304 # File path provided as taint input
      - G404 # Insecure random number source

  # Nil checks
  nilnil:
    checked-types:
      - ptr
      - func
      - iface
      - map
      - chan

  # Error handling
  errorlint:
    errorf: true
    asserts: true
    comparison: true

  # Struct tag validation
  tagliatelle:
    case:
      rules:
        json: snake
        yaml: snake
        xml: snake
        bson: snake
        avro: snake
        mapstructure: snake

  # Variable naming
  varnamelen:
    min-name-length: 2
    ignore-type-assert-ok: true
    ignore-map-index-ok: true
    ignore-chan-recv-ok: true
    ignore-names:
      - err
      - ok
      - id
      - ip
      - db
      - tx
      - wg
      - mu
    ignore-decls:
      - c echo.Context
      - t testing.T
      - f *foo.Bar
      - e error
      - i int
      - const C
      - T any
      - m map[string]int

  # Exhaustive switch/type checks
  exhaustive:
    check-generated: false
    default-signifies-exhaustive: true

  # Interface naming
  ireturn:
    allow:
      - anon
      - error
      - empty
      - stdlib
      - generic

  # Maintainability index
  maintidx:
    under: 20

  # Nesting depth
  nestif:
    min-complexity: 5

  # Unused parameters
  unparam:
    check-exported: false

  # Whitespace
  wsl:
    strict-append: true
    allow-assign-and-call: true
    allow-multiline-assign: true
    allow-separated-leading-comment: false
    allow-trailing-comment: false
    force-case-trailing-whitespace: 0
    force-err-cuddling: false
    force-short-decl-cuddling: false

linters:
  disable-all: true
  enable:
    # Enabled by default
    - errcheck
    - gosimple
    - govet
    - ineffassign
    - staticcheck
    - typecheck
    - unused

    # Additional useful linters
    - asciicheck
    - bidichk
    - bodyclose
    - containedctx
    - contextcheck
    - cyclop
    - decorder
    - depguard
    - dogsled
    - dupl
    - durationcheck
    - errname
    - errorlint
    - execinquery
    - exhaustive
    - exportloopref
    - forbidigo
    - forcetypeassert
    - funlen
    - gci
    - gochecknoglobals
    - gochecknoinits
    - gocognit
    - goconst
    - gocritic
    - gocyclo
    - godot
    - gofmt
    - gofumpt
    - goheader
    - goimports
    - gomnd
    - gomoddirectives
    - gomodguard
    - goprintffuncname
    - gosec
    - grouper
    - importas
    - ireturn
    - lll
    - loggercheck
    - maintidx
    - makezero
    - misspell
    - nakedret
    - nestif
    - nilerr
    - nilnil
    - nlreturn
    - noctx
    - nolintlint
    - nonamedreturns
    - nosprintfhostport
    - predeclared
    - promlinter
    - reassign
    - revive
    - rowserrcheck
    - sqlclosecheck
    - stylecheck
    - tagliatelle
    - tenv
    - testableexamples
    - thelper
    - tparallel
    - unconvert
    - unparam
    - usestdlibvars
    - varnamelen
    - wastedassign
    - whitespace
    - wrapcheck
    - wsl

issues:
  exclude-rules:
    # Test files
    - path: _test\.go
      linters:
        - gomnd
        - funlen
        - goconst
        - gochecknoglobals
        - varnamelen
        - wrapcheck
        - nlreturn
        - wsl
        - lll

    # Main/cmd files
    - path: cmd/
      linters:
        - gomnd
        - gochecknoglobals
        - wrapcheck

    # Tool implementations
    - path: internal/tools/
      linters:
        - funlen
        - gocognit
        - cyclop

    # Generated files
    - path: ".*\\.gen\\.go"
      linters:
        - all

    # Mock files
    - path: mock_.*\.go
      linters:
        - all

    # Exclude specific error messages
    - text: "Error return value of .((os\\.)?std(out|err)\\.(.*)|.*Close|.*Flush|os\\.Remove(All)?|.*print(f|ln)?|os\\.(Un)?Setenv). is not checked"
      linters:
        - errcheck

    - text: "should not use dot imports"
      linters:
        - revive

    - text: "package-comments"
      linters:
        - revive

    - text: "ST1000"
      linters:
        - stylecheck

  exclude-use-default: false
  max-issues-per-linter: 50
  max-same-issues: 3
  new: false
  new-from-rev: ""
  new-from-patch: ""
  whole-files: false
  fix: false

severity:
  default-severity: error
  case-sensitive: false
  rules:
    - linters:
        - dupl
        - gomnd
        - goconst
      severity: warning
    - linters:
        - lll
        - wsl
        - nlreturn
      severity: info
