#!/bin/bash

# Build script for Tailscale MCP Server
# Usage: ./scripts/build.sh [options]

set -euo pipefail

# Default values
VERSION=${VERSION:-$(git describe --tags --always --dirty 2>/dev/null || echo "dev")}
COMMIT=${COMMIT:-$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")}
BUILD_TIME=${BUILD_TIME:-$(date -u +"%Y-%m-%dT%H:%M:%SZ")}
OUTPUT_DIR=${OUTPUT_DIR:-"bin"}
BINARY_NAME=${BINARY_NAME:-"tailscale-mcp-server"}

# Build flags - Fixed LDFLAGS syntax
LDFLAGS="-X github.com/hexsleeves/tailscale-mcp-server/version.Version=${VERSION} \
         -X github.com/hexsleeves/tailscale-mcp-server/version.GitCommit=${COMMIT} \
         -X github.com/hexsleeves/tailscale-mcp-server/version.BuildTime=${BUILD_TIME}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Go is installed
check_go() {
    if ! command -v go >/dev/null 2>&1; then
        log_error "Go is not installed or not in PATH"
        exit 1
    fi

    log_info "Go version: $(go version)"
}

# Parse command line arguments
CROSS_COMPILE=false
CLEAN=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
    --cross-compile)
        CROSS_COMPILE=true
        shift
        ;;
    --clean)
        CLEAN=true
        shift
        ;;
    --verbose)
        VERBOSE=true
        shift
        ;;
    --help)
        echo "Usage: $0 [options]"
        echo "Options:"
        echo "  --cross-compile  Build for all supported platforms"
        echo "  --clean          Clean build artifacts before building"
        echo "  --verbose        Enable verbose output"
        echo "  --help           Show this help message"
        exit 0
        ;;
    *)
        log_error "Unknown option: $1"
        exit 1
        ;;
    esac
done

# Check prerequisites
check_go

# Clean if requested
if [[ "$CLEAN" == "true" ]]; then
    log_info "Cleaning build artifacts..."
    rm -rf "$OUTPUT_DIR"
    rm -rf dist/
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Build information
log_info "Building Tailscale MCP Server"
log_info "Version: $VERSION"
log_info "Commit: $COMMIT"
log_info "Build Time: $BUILD_TIME"

# Function to build for a specific platform
build_binary() {
    local goos=$1
    local goarch=$2
    local output_name=$3

    log_info "Building for $goos/$goarch..."

    local build_cmd="go build -ldflags \"$LDFLAGS\" -o \"$output_name\" ./cmd/tailscale-mcp-server"

    if [[ "$VERBOSE" == "true" ]]; then
        log_info "Executing: GOOS=$goos GOARCH=$goarch $build_cmd"
        GOOS="$goos" GOARCH="$goarch" eval "$build_cmd"
    else
        GOOS="$goos" GOARCH="$goarch" eval "$build_cmd" 2>/dev/null
    fi

    if [[ $? -eq 0 ]]; then
        log_info "✓ Built $output_name"
        return 0
    else
        log_error "✗ Failed to build for $goos/$goarch"
        return 1
    fi
}

if [[ "$CROSS_COMPILE" == "true" ]]; then
    # Cross-compile for multiple platforms
    platforms=(
        "linux/amd64"
        "linux/arm64"
        "darwin/amd64"
        "darwin/arm64"
        "windows/amd64"
    )

    failed_builds=0

    for platform in "${platforms[@]}"; do
        IFS='/' read -r GOOS GOARCH <<<"$platform"
        output_name="$OUTPUT_DIR/${BINARY_NAME}-${GOOS}-${GOARCH}"

        if [[ "$GOOS" == "windows" ]]; then
            output_name="${output_name}.exe"
        fi

        if ! build_binary "$GOOS" "$GOARCH" "$output_name"; then
            ((failed_builds++))
        fi
    done

    if [[ $failed_builds -gt 0 ]]; then
        log_error "$failed_builds build(s) failed"
        exit 1
    fi
else
    # Build for current platform
    current_os=$(go env GOOS)
    current_arch=$(go env GOARCH)
    output_name="$OUTPUT_DIR/$BINARY_NAME"

    if [[ "$current_os" == "windows" ]]; then
        output_name="${output_name}.exe"
    fi

    log_info "Building for current platform ($current_os/$current_arch)..."

    if ! build_binary "$current_os" "$current_arch" "$output_name"; then
        exit 1
    fi

    # Make executable (not needed on Windows)
    if [[ "$current_os" != "windows" ]]; then
        chmod +x "$output_name"
    fi

    # Show binary info
    if command -v file >/dev/null 2>&1 && [[ "$current_os" != "windows" ]]; then
        log_info "Binary info: $(file "$output_name")"
    fi

    # Show size
    if command -v du >/dev/null 2>&1; then
        size=$(du -h "$output_name" | cut -f1)
        log_info "Binary size: $size"
    elif command -v ls >/dev/null 2>&1; then
        # Fallback for systems without du
        size=$(ls -lh "$output_name" | awk '{print $5}')
        log_info "Binary size: $size"
    fi
fi

log_info "Build completed successfully!"

# Show all built binaries
log_info "Built binaries:"
find "$OUTPUT_DIR" -name "${BINARY_NAME}*" -type f | while read -r binary; do
    log_info "  $(basename "$binary")"
done
