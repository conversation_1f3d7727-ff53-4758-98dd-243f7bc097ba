# tailscale-mcp-server/Makefile
.PHONY: run build test lint clean install docker

run:
	go run ./cmd/tailscale-mcp-server/main.go $(ARGS) $(filter-out $@ --,$(MAKECMDGOALS))

%::
	@:

# Build the binary
build:
	./scripts/build.sh

# Install the binary
install:
	go install $(LDFLAGS) ./cmd/tailscale-mcp-server

# Run tests
test:
	go test -v ./...

# Run integration tests
test-integration:
	go test -tags=integration -v ./test/integration/...

# Run linter
lint:
	golangci-lint run

lint-fix:
	golangci-lint run --fix

# Clean build artifacts
clean:
	rm -rf bin/ dist/

# Build Docker image
docker:
	docker build -t tailscale-mcp-server:$(VERSION) .

# Development setup
dev-setup:
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go mod download
