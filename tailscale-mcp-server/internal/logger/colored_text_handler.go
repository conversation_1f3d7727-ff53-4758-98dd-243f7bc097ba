package logger

import (
	"context"
	"io"
	"log/slog"
	"strings"
)

// ANSI color codes
const (
	ColorReset  = "\033[0m"
	ColorRed    = "\033[31m"
	ColorYellow = "\033[33m"
	ColorBlue   = "\033[34m"
	ColorGray   = "\033[37m"
	ColorBold   = "\033[1m"
)

var levelToColor = map[slog.Level]string{
	slog.LevelDebug: ColorGray,
	slog.LevelInfo:  ColorBlue,
	slog.LevelWarn:  ColorYellow,
	slog.LevelError: ColorRed,
}

// ColoredTextHandler implements slog.Handler with color support
type ColoredTextHandler struct {
	writer   io.Writer
	opts     *slog.HandlerOptions
	useColor bool
}

// NewColoredTextHandler creates a new colored text handler
func NewColoredTextHandler(w io.Writer, opts *slog.HandlerOptions, useColor bool) *ColoredTextHandler {
	return &ColoredTextHandler{
		writer:   w,
		opts:     opts,
		useColor: useColor,
	}
}

// Enabled reports whether the handler handles records at the given level
func (h *ColoredTextHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return level >= h.opts.Level.Level()
}

// Handle implements slog.Handler interface with color support
func (h *ColoredTextHandler) Handle(ctx context.Context, r slog.Record) error {
	if !h.Enabled(ctx, r.Level) {
		return nil
	}

	buf := make([]byte, 0, 1024)

	// Time
	buf = append(buf, r.Time.Format("2006/01/02 15:04:05")...)
	buf = append(buf, ' ')

	// Level with optional color
	levelStr := r.Level.String()
	if h.useColor {
		colorCode := levelToColor[r.Level]
		buf = append(buf, colorCode+ColorBold+strings.ToUpper(levelStr)+ColorReset...)
	} else {
		buf = append(buf, strings.ToUpper(levelStr)...)
	}
	buf = append(buf, ' ')

	// Note: Source information is handled by slog internally when AddSource is true
	// We don't need to manually add it here as it's part of the record processing

	// Message
	buf = append(buf, r.Message...)

	// Add attributes
	r.Attrs(func(a slog.Attr) bool {
		buf = append(buf, ' ')
		buf = append(buf, a.Key...)
		buf = append(buf, '=')
		buf = append(buf, a.Value.String()...)
		return true
	})

	buf = append(buf, '\n')

	// Write to the output
	_, err := h.writer.Write(buf)
	return err
}

// WithAttrs returns a new handler with the given attributes
func (h *ColoredTextHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	// For simplicity, we'll create a new handler that wraps the attributes
	// This is a basic implementation - a full implementation would need to handle this properly
	return h
}

// WithGroup returns a new handler with the given group
func (h *ColoredTextHandler) WithGroup(name string) slog.Handler {
	// For simplicity, we'll return the same handler
	// A full implementation would need to handle grouping properly
	return h
}
