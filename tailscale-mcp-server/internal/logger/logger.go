package logger

import (
	"fmt"
	"io"
	"log/slog"
	"os"
	"strings"
	"sync"

	"github.com/joho/godotenv"
)

var (
	initialized  bool
	globalLogger *slog.Logger
	loggerMutex  sync.RWMutex
)

// Initialize sets up the global logger with the specified level and optional file output
func Initialize(level int, logFile string) error {
	loggerMutex.Lock()
	defer loggerMutex.Unlock()

	// Load .env file
	if err := godotenv.Load(); err != nil {
		fmt.Fprintf(os.Stderr, "Warning: No .env file found or unable to load: %v\n", err)
	}

	// Determine log level from environment variable or parameter
	var slogLevel slog.Level
	if logLevelStr := os.Getenv("LOG_LEVEL"); logLevelStr != "" {
		switch strings.ToLower(logLevelStr) {
		case "debug":
			slogLevel = slog.LevelDebug
		case "info":
			slogLevel = slog.LevelInfo
		case "warn", "warning":
			slogLevel = slog.LevelWarn
		case "error":
			slogLevel = slog.LevelError
		default:
			fmt.Printf("Invalid LOG_LEVEL environment variable '%s', defaulting to info\n", logLevelStr)
			slogLevel = slog.LevelInfo
		}
	} else {
		// Use the passed level parameter
		switch level {
		case 0:
			slogLevel = slog.LevelDebug
		case 1:
			slogLevel = slog.LevelInfo
		case 2:
			slogLevel = slog.LevelWarn
		case 3:
			slogLevel = slog.LevelError
		default:
			slogLevel = slog.LevelInfo
		}
	}

	// Determine output writers
	var writers []io.Writer
	writers = append(writers, os.Stderr)

	if logFile != "" {
		file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return fmt.Errorf("failed to open log file: %w", err)
		}
		writers = append(writers, file)
	}

	var output io.Writer
	if len(writers) == 1 {
		output = writers[0]
	} else {
		output = io.MultiWriter(writers...)
	}

	// Create handler options
	opts := &slog.HandlerOptions{
		Level:     slogLevel,
		AddSource: true, // Equivalent to zap's AddCaller
	}

	// Check if colors should be disabled
	useColor := true
	if colorEnv := strings.ToLower(os.Getenv("LOG_COLOR")); colorEnv == "false" || colorEnv == "0" || colorEnv == "no" {
		useColor = false
	}

	// Also disable colors if we're not outputting to a terminal or if NO_COLOR is set
	if os.Getenv("NO_COLOR") != "" {
		useColor = false
	}

	// Disable colors when writing to a file (unless explicitly enabled)
	if logFile != "" && os.Getenv("LOG_COLOR") == "" {
		useColor = false
	}

	// Create appropriate handler based on format preference
	var handler slog.Handler
	switch strings.ToLower(os.Getenv("LOG_FORMAT")) {
	case "json":
		handler = slog.NewJSONHandler(output, opts)
	default:
		// Use colored text handler for console output
		handler = NewColoredTextHandler(output, opts, useColor)
	}

	// Create the logger
	globalLogger = slog.New(handler)
	initialized = true

	return nil
}

// Cleanup properly closes the logger and flushes any buffered log entries
func Cleanup() error {
	loggerMutex.RLock()
	defer loggerMutex.RUnlock()

	// slog doesn't require explicit cleanup like zap, but we maintain the interface
	return nil
}

// isInitialized checks if the logger has been initialized
func isInitialized() bool {
	loggerMutex.RLock()
	defer loggerMutex.RUnlock()
	return initialized && globalLogger != nil
}

// Debug logs a debug message
func Debug(msg string, args ...any) {
	if !isInitialized() {
		fmt.Fprintf(os.Stderr, "DEBUG (logger not initialized): %s\n", msg)
		return
	}
	globalLogger.Debug(msg, convertArgsToSlogAttrs(args)...)
}

// Info logs an info message
func Info(msg string, args ...any) {
	if !isInitialized() {
		fmt.Fprintf(os.Stderr, "INFO (logger not initialized): %s\n", msg)
		return
	}
	globalLogger.Info(msg, convertArgsToSlogAttrs(args)...)
}

// Warn logs a warning message
func Warn(msg string, args ...any) {
	if !isInitialized() {
		fmt.Fprintf(os.Stderr, "WARN (logger not initialized): %s\n", msg)
		return
	}
	globalLogger.Warn(msg, convertArgsToSlogAttrs(args)...)
}

// Error logs an error message
func Error(msg string, args ...any) {
	if !isInitialized() {
		fmt.Fprintf(os.Stderr, "ERROR (logger not initialized): %s\n", msg)
		return
	}
	globalLogger.Error(msg, convertArgsToSlogAttrs(args)...)
}

// Fatal logs a fatal error message and exits
func Fatal(msg string, args ...any) {
	if isInitialized() {
		globalLogger.Error(msg, convertArgsToSlogAttrs(args)...)
	} else {
		// Fallback if logger is nil
		fmt.Fprintf(os.Stderr, "FATAL (logger not initialized): %s\n", msg)
	}
	os.Exit(1)
}

// With returns a logger with the given attributes
func With(args ...any) *slog.Logger {
	if isInitialized() {
		return globalLogger.With(convertArgsToSlogAttrs(args)...)
	}
	return slog.New(slog.NewTextHandler(io.Discard, nil)).With(convertArgsToSlogAttrs(args)...)
}

// GetLogger returns the global logger instance
func GetLogger() *slog.Logger {
	if isInitialized() {
		return globalLogger
	}
	return slog.New(slog.NewTextHandler(io.Discard, nil))
}

// Helper function to convert variadic arguments to slog.Attr slices
func convertArgsToSlogAttrs(args []any) []any {
	if len(args) == 0 {
		return nil
	}

	var attrs []any
	for i := 0; i < len(args); i += 2 {
		if i+1 >= len(args) {
			// Odd number of args - log the orphaned key
			attrs = append(attrs, slog.Any("orphaned_key", args[i]))
			break
		}

		key, ok := args[i].(string)
		if !ok {
			// Non-string key - convert to string
			key = fmt.Sprintf("%v", args[i])
		}

		attrs = append(attrs, slog.Any(key, args[i+1]))
	}
	return attrs
}
