package mcp

import (
	"github.com/hexsleeves/tailscale-mcp-server/internal/config"
	"github.com/hexsleeves/tailscale-mcp-server/internal/tailscale"
	"github.com/hexsleeves/tailscale-mcp-server/internal/tools"
	"github.com/mark3labs/mcp-go/server"
)

// MCPServer implements the MCP server interface, handling tool registration and execution.
type MCPServer struct {
	server   *server.MCPServer
	registry *tools.ToolRegistry
}

// NewMCPServer creates a new MCP server instance.
func NewMCPServer(cfg *config.Config) (*MCPServer, error) {
	// Initialize MCP server
	mcpServer := server.NewMCPServer(
		"tailscale-mcp-server",
		"0.1.0",
		server.WithResourceCapabilities(true, true),
		server.WithPromptCapabilities(true),
		server.WithToolCapabilities(true),
	)

	// Initialize Tailscale clients
	api := tailscale.NewAPIClient(cfg)
	cli, err := tailscale.NewTailscaleCLI()
	if err != nil {
		return nil, err
	}

	// Register tools
	registry := tools.NewToolRegistry(api, cli)

	server := &MCPServer{
		server:   mcpServer,
		registry: registry,
	}

	return server, nil
}

func (s *MCPServer) ServeHTTP() *server.StreamableHTTPServer {
	return server.NewStreamableHTTPServer(s.server)
}

func (s *MCPServer) ServeStdio() error {
	return server.ServeStdio(s.server)
}
