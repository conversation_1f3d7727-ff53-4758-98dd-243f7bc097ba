package tools

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/hexsleeves/tailscale-mcp-server/internal/tailscale"
)

// NetworkTool provides network management functionality
type NetworkTool struct {
	cli *tailscale.TailscaleCLI
	api *tailscale.APIClient
}

// NewNetworkTool creates a new network tool
func NewNetworkTool(cli *tailscale.TailscaleCLI, api *tailscale.APIClient) *NetworkTool {
	return &NetworkTool{
		cli: cli,
		api: api,
	}
}

// Name returns the tool name
func (n *NetworkTool) Name() string {
	return "network"
}

// Description returns the tool description
func (n *NetworkTool) Description() string {
	return "Network operations including ping, connectivity tests, and route management"
}

// InputSchema returns the JSON schema for tool input
func (n *NetworkTool) InputSchema() any {
	return map[string]any{
		"type": "object",
		"properties": map[string]any{
			"action": map[string]any{
				"type":        "string",
				"description": "Network action to perform",
				"enum":        []string{"ping", "routes", "connectivity", "ip"},
			},
			"target": map[string]any{
				"type":        "string",
				"description": "Target host or IP for network operations",
			},
			"count": map[string]any{
				"type":        "integer",
				"description": "Number of ping packets to send",
				"default":     4,
			},
		},
		"required": []string{"action"},
	}
}

// Execute runs the network tool
func (n *NetworkTool) Execute(ctx context.Context, args json.RawMessage) (string, error) {
	var input map[string]any
	if err := json.Unmarshal(args, &input); err != nil {
		return "", fmt.Errorf("failed to unmarshal input: %w", err)
	}

	action, ok := input["action"].(string)
	if !ok {
		return "", fmt.Errorf("action is required and must be a string")
	}

	var result any
	var err error

	switch action {
	case "ping":
		target, _ := input["target"].(string)
		count, _ := input["count"].(float64)
		if count == 0 {
			count = 4
		}
		result, err = n.ping(ctx, target, int(count))
	case "routes":
		result, err = n.getRoutes(ctx)
	case "connectivity":
		result, err = n.testConnectivity(ctx)
	case "ip":
		result, err = n.getIP(ctx)
	default:
		err = fmt.Errorf("unsupported action: %s", action)
	}

	if err != nil {
		return "", err
	}

	output, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("failed to marshal result: %w", err)
	}

	return string(output), nil
}

func (n *NetworkTool) ping(ctx context.Context, target string, count int) (any, error) {
	if target == "" {
		return nil, fmt.Errorf("target is required for ping action")
	}

	result, err := n.cli.Ping(target, count)
	if err != nil {
		return nil, fmt.Errorf("ping failed: %w", err)
	}

	return result, nil
}

func (n *NetworkTool) getRoutes(ctx context.Context) (any, error) {
	// Implementation for getting routes
	result, err := n.cli.GetStatus()
	if err != nil {
		return nil, fmt.Errorf("failed to get routes: %w", err)
	}

	return map[string]any{
		"routes":  result,
		"message": "Route information retrieved",
	}, nil
}

func (n *NetworkTool) testConnectivity(ctx context.Context) (any, error) {
	// Test basic connectivity using netcheck
	result, err := n.cli.Netcheck()
	if err != nil {
		return nil, fmt.Errorf("connectivity test failed: %w", err)
	}

	return map[string]any{
		"connected": true,
		"result":    result,
		"message":   "Connectivity test successful",
	}, nil
}

func (n *NetworkTool) getIP(ctx context.Context) (any, error) {
	result, err := n.cli.IP()
	if err != nil {
		return nil, fmt.Errorf("failed to get IP: %w", err)
	}

	return result, nil
}
