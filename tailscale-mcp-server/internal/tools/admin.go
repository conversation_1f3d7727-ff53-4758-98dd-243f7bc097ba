package tools

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/hexsleeves/tailscale-mcp-server/internal/tailscale"
)

// AdminTool provides administrative functionality
type AdminTool struct {
	cli *tailscale.TailscaleCLI
	api *tailscale.APIClient
}

// NewAdminTool creates a new admin tool
func NewAdminTool(cli *tailscale.TailscaleCLI, api *tailscale.APIClient) *AdminTool {
	return &AdminTool{
		cli: cli,
		api: api,
	}
}

// Name returns the tool name
func (a *AdminTool) Name() string {
	return "admin"
}

// Description returns the tool description
func (a *AdminTool) Description() string {
	return "Administrative operations including user management, settings, and system configuration"
}

// InputSchema returns the JSON schema for tool input
func (a *AdminTool) InputSchema() any {
	return map[string]any{
		"type": "object",
		"properties": map[string]any{
			"action": map[string]any{
				"type":        "string",
				"description": "Administrative action to perform",
				"enum":        []string{"status", "logout", "login", "up", "down", "version"},
			},
			"auth_key": map[string]any{
				"type":        "string",
				"description": "Authentication key for login operations",
			},
			"hostname": map[string]any{
				"type":        "string",
				"description": "Hostname for the device",
			},
		},
		"required": []string{"action"},
	}
}

// Execute runs the admin tool
func (a *AdminTool) Execute(ctx context.Context, args json.RawMessage) (string, error) {
	var input map[string]any
	if err := json.Unmarshal(args, &input); err != nil {
		return "", fmt.Errorf("failed to unmarshal input: %w", err)
	}

	action, ok := input["action"].(string)
	if !ok {
		return "", fmt.Errorf("action is required and must be a string")
	}

	var result any
	var err error

	switch action {
	case "status":
		result, err = a.getStatus(ctx)
	case "logout":
		result, err = a.logout(ctx)
	case "login":
		authKey, _ := input["auth_key"].(string)
		result, err = a.login(ctx, authKey)
	case "up":
		hostname, _ := input["hostname"].(string)
		result, err = a.up(ctx, hostname)
	case "down":
		result, err = a.down(ctx)
	case "version":
		result, err = a.getVersion(ctx)
	default:
		err = fmt.Errorf("unsupported action: %s", action)
	}

	if err != nil {
		return "", err
	}

	output, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("failed to marshal result: %w", err)
	}

	return string(output), nil
}

func (a *AdminTool) getStatus(ctx context.Context) (any, error) {
	result, err := a.cli.GetStatus()
	if err != nil {
		return nil, fmt.Errorf("failed to get status: %w", err)
	}

	return result, nil
}

func (a *AdminTool) logout(ctx context.Context) (any, error) {
	err := a.cli.Logout()
	if err != nil {
		return nil, fmt.Errorf("logout failed: %w", err)
	}

	return map[string]any{
		"success": true,
		"message": "Successfully logged out",
	}, nil
}

func (a *AdminTool) login(ctx context.Context, authKey string) (any, error) {
	if authKey == "" {
		return nil, fmt.Errorf("auth_key is required for login action")
	}

	// Use Up command with auth key for login
	options := &tailscale.UpOptions{
		AuthKey: authKey,
	}
	err := a.cli.Up(options)
	if err != nil {
		return nil, fmt.Errorf("login failed: %w", err)
	}

	return map[string]any{
		"success": true,
		"message": "Successfully logged in",
	}, nil
}

func (a *AdminTool) up(ctx context.Context, hostname string) (any, error) {
	options := &tailscale.UpOptions{}
	if hostname != "" {
		options.Hostname = hostname
	}

	err := a.cli.Up(options)
	if err != nil {
		return nil, fmt.Errorf("up command failed: %w", err)
	}

	return map[string]any{
		"success": true,
		"message": "Tailscale is now up",
	}, nil
}

func (a *AdminTool) down(ctx context.Context) (any, error) {
	err := a.cli.Down()
	if err != nil {
		return nil, fmt.Errorf("down command failed: %w", err)
	}

	return map[string]any{
		"success": true,
		"message": "Tailscale is now down",
	}, nil
}

func (a *AdminTool) getVersion(ctx context.Context) (any, error) {
	result, err := a.cli.GetVersion()
	if err != nil {
		return nil, fmt.Errorf("failed to get version: %w", err)
	}

	return map[string]any{
		"version": result,
	}, nil
}
