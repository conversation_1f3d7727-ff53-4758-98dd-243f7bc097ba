package tools

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/hexsleeves/tailscale-mcp-server/internal/tailscale"
)

// ACLTool provides Access Control List management functionality
type ACLTool struct {
	cli *tailscale.TailscaleCLI
	api *tailscale.APIClient
}

// NewACLTool creates a new ACL tool
func NewACLTool(cli *tailscale.TailscaleCLI, api *tailscale.APIClient) *ACLTool {
	return &ACLTool{
		cli: cli,
		api: api,
	}
}

// Name returns the tool name
func (a *ACLTool) Name() string {
	return "acl"
}

// Description returns the tool description
func (a *ACLTool) Description() string {
	return "Access Control List management including viewing, updating, and validating ACL policies"
}

// InputSchema returns the JSON schema for tool input
func (a *ACLTool) InputSchema() any {
	return map[string]any{
		"type": "object",
		"properties": map[string]any{
			"action": map[string]any{
				"type":        "string",
				"description": "ACL action to perform",
				"enum":        []string{"get", "set", "validate", "test"},
			},
			"policy": map[string]any{
				"type":        "string",
				"description": "ACL policy JSON for set operations",
			},
			"source": map[string]any{
				"type":        "string",
				"description": "Source IP or user for ACL testing",
			},
			"destination": map[string]any{
				"type":        "string",
				"description": "Destination IP or service for ACL testing",
			},
			"port": map[string]any{
				"type":        "integer",
				"description": "Port number for ACL testing",
			},
		},
		"required": []string{"action"},
	}
}

// Execute runs the ACL tool
func (a *ACLTool) Execute(ctx context.Context, args json.RawMessage) (string, error) {
	var input map[string]any
	if err := json.Unmarshal(args, &input); err != nil {
		return "", fmt.Errorf("failed to unmarshal input: %w", err)
	}

	action, ok := input["action"].(string)
	if !ok {
		return "", fmt.Errorf("action is required and must be a string")
	}

	var result any
	var err error

	switch action {
	case "get":
		result, err = a.getACL(ctx)
	case "set":
		policy, _ := input["policy"].(string)
		result, err = a.setACL(ctx, policy)
	case "validate":
		policy, _ := input["policy"].(string)
		result, err = a.validateACL(ctx, policy)
	case "test":
		source, _ := input["source"].(string)
		destination, _ := input["destination"].(string)
		port, _ := input["port"].(float64)
		result, err = a.testACL(ctx, source, destination, int(port))
	default:
		err = fmt.Errorf("unsupported action: %s", action)
	}

	if err != nil {
		return "", err
	}

	output, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("failed to marshal result: %w", err)
	}

	return string(output), nil
}

func (a *ACLTool) getACL(ctx context.Context) (any, error) {
	if a.api != nil {
		acl := a.api.GetACL(ctx) // ← single result
		return acl, nil
	}

	return nil, fmt.Errorf("ACL retrieval not available – requires API access")
}

func (a *ACLTool) setACL(ctx context.Context, policy string) (any, error) {
	if policy == "" {
		return nil, fmt.Errorf("policy is required for set action")
	}

	// SetACL method not implemented in API client yet
	return nil, fmt.Errorf("ACL modification not available - SetACL method not implemented")
}

func (a *ACLTool) validateACL(ctx context.Context, policy string) (any, error) {
	if policy == "" {
		return nil, fmt.Errorf("policy is required for validate action")
	}

	// ValidateACL method not implemented in API client yet
	return nil, fmt.Errorf("ACL validation not available - ValidateACL method not implemented")
}

func (a *ACLTool) testACL(ctx context.Context, source, destination string, port int) (any, error) {
	if source == "" || destination == "" {
		return nil, fmt.Errorf("source and destination are required for test action")
	}

	// TestACL method not implemented in API client yet
	return nil, fmt.Errorf("ACL testing not available - TestACL method not implemented")
}
