version: "3.8"

services:
  tailscale-mcp:
    build: .
    restart: unless-stopped
    container_name: tailscale-mcp-server
    networks:
      - mcp-network
    volumes:
      - ./logs:/app/logs
    environment:
      - TAILSCALE_API_KEY=${TAILSCALE_API_KEY}
      - TAILSCALE_TAILNET=${TAILSCALE_TAILNET}
      - MCP_SERVER_LOG_FILE=/app/logs/server.log
      - NODE_ENV=production
    healthcheck:
      test: ["CMD", "node", "-e", "process.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  mcp-network:
    driver: bridge
