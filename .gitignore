# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build artifacts
dist/
build/
# IDE
.vscode/
.idea/

# Logs
*.log

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Coverage reports
coverage.out
coverage.html

# Mac OS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# Temporary files
*.tmp
*.temp

# Added by Task Master AI
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
…
# IDE / Editor settings
.vscode/
.idea/
*.sw?
# OS specific
# Task files
tasks.json
tasks/

# Added by Task Master AI
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln

# AI Stuff
.roo
.roomodes
.aider*
